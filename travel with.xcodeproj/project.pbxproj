// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		846F439A2E39352A0037587F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 846F43822E3935240037587F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 846F43892E3935240037587F;
			remoteInfo = "travel with";
		};
		846F43A42E39352A0037587F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 846F43822E3935240037587F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 846F43892E3935240037587F;
			remoteInfo = "travel with";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		846F438A2E3935240037587F /* travel with.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "travel with.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		846F43992E39352A0037587F /* travel withTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "travel withTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		846F43A32E39352A0037587F /* travel withUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "travel withUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		846F438C2E3935240037587F /* travel with */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "travel with";
			sourceTree = "<group>";
		};
		846F439C2E39352A0037587F /* travel withTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "travel withTests";
			sourceTree = "<group>";
		};
		846F43A62E39352A0037587F /* travel withUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "travel withUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		846F43872E3935240037587F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		846F43962E39352A0037587F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		846F43A02E39352A0037587F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		846F43812E3935240037587F = {
			isa = PBXGroup;
			children = (
				846F438C2E3935240037587F /* travel with */,
				846F439C2E39352A0037587F /* travel withTests */,
				846F43A62E39352A0037587F /* travel withUITests */,
				846F438B2E3935240037587F /* Products */,
			);
			sourceTree = "<group>";
		};
		846F438B2E3935240037587F /* Products */ = {
			isa = PBXGroup;
			children = (
				846F438A2E3935240037587F /* travel with.app */,
				846F43992E39352A0037587F /* travel withTests.xctest */,
				846F43A32E39352A0037587F /* travel withUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		846F43892E3935240037587F /* travel with */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 846F43AD2E39352A0037587F /* Build configuration list for PBXNativeTarget "travel with" */;
			buildPhases = (
				846F43862E3935240037587F /* Sources */,
				846F43872E3935240037587F /* Frameworks */,
				846F43882E3935240037587F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				846F438C2E3935240037587F /* travel with */,
			);
			name = "travel with";
			packageProductDependencies = (
			);
			productName = "travel with";
			productReference = 846F438A2E3935240037587F /* travel with.app */;
			productType = "com.apple.product-type.application";
		};
		846F43982E39352A0037587F /* travel withTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 846F43B02E39352A0037587F /* Build configuration list for PBXNativeTarget "travel withTests" */;
			buildPhases = (
				846F43952E39352A0037587F /* Sources */,
				846F43962E39352A0037587F /* Frameworks */,
				846F43972E39352A0037587F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				846F439B2E39352A0037587F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				846F439C2E39352A0037587F /* travel withTests */,
			);
			name = "travel withTests";
			packageProductDependencies = (
			);
			productName = "travel withTests";
			productReference = 846F43992E39352A0037587F /* travel withTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		846F43A22E39352A0037587F /* travel withUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 846F43B32E39352A0037587F /* Build configuration list for PBXNativeTarget "travel withUITests" */;
			buildPhases = (
				846F439F2E39352A0037587F /* Sources */,
				846F43A02E39352A0037587F /* Frameworks */,
				846F43A12E39352A0037587F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				846F43A52E39352A0037587F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				846F43A62E39352A0037587F /* travel withUITests */,
			);
			name = "travel withUITests";
			packageProductDependencies = (
			);
			productName = "travel withUITests";
			productReference = 846F43A32E39352A0037587F /* travel withUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		846F43822E3935240037587F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					846F43892E3935240037587F = {
						CreatedOnToolsVersion = 16.4;
					};
					846F43982E39352A0037587F = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 846F43892E3935240037587F;
					};
					846F43A22E39352A0037587F = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 846F43892E3935240037587F;
					};
				};
			};
			buildConfigurationList = 846F43852E3935240037587F /* Build configuration list for PBXProject "travel with" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 846F43812E3935240037587F;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 846F438B2E3935240037587F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				846F43892E3935240037587F /* travel with */,
				846F43982E39352A0037587F /* travel withTests */,
				846F43A22E39352A0037587F /* travel withUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		846F43882E3935240037587F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		846F43972E39352A0037587F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		846F43A12E39352A0037587F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		846F43862E3935240037587F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		846F43952E39352A0037587F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		846F439F2E39352A0037587F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		846F439B2E39352A0037587F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 846F43892E3935240037587F /* travel with */;
			targetProxy = 846F439A2E39352A0037587F /* PBXContainerItemProxy */;
		};
		846F43A52E39352A0037587F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 846F43892E3935240037587F /* travel with */;
			targetProxy = 846F43A42E39352A0037587F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		846F43AB2E39352A0037587F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		846F43AC2E39352A0037587F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		846F43AE2E39352A0037587F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SBGMFT5HZ9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "travel with";
				INFOPLIST_KEY_LSApplicationQueriesSchemes = "diditaxi iosamap baidumap qqmap maps";
				INFOPLIST_KEY_NSCameraUsageDescription = "Travel With需要使用相机来提供AR导航功能，让您的AI朋友能够看到周围环境并提供更好的旅行建议。";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Travel With需要访问您的位置信息来提供精准的导航服务和个性化的旅行建议。";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Travel With需要使用麦克风进行语音识别，通过火山引擎ASR将您的语音转换为文字发送给AI朋友。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Travel With需要访问您的照片库，以便您可以与AI朋友分享旅行照片并获得智能分析和建议。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = arkit;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.alian.travel-with";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		846F43AF2E39352A0037587F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SBGMFT5HZ9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "travel with";
				INFOPLIST_KEY_LSApplicationQueriesSchemes = "diditaxi iosamap baidumap qqmap maps";
				INFOPLIST_KEY_NSCameraUsageDescription = "Travel With需要使用相机来提供AR导航功能，让您的AI朋友能够看到周围环境并提供更好的旅行建议。";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Travel With需要访问您的位置信息来提供精准的导航服务和个性化的旅行建议。";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Travel With需要使用麦克风进行语音识别，通过火山引擎ASR将您的语音转换为文字发送给AI朋友。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Travel With需要访问您的照片库，以便您可以与AI朋友分享旅行照片并获得智能分析和建议。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = arkit;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.alian.travel-with";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		846F43B12E39352A0037587F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.alian.travel-withTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/travel with.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/travel with";
			};
			name = Debug;
		};
		846F43B22E39352A0037587F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.alian.travel-withTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/travel with.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/travel with";
			};
			name = Release;
		};
		846F43B42E39352A0037587F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.alian.travel-withUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "travel with";
			};
			name = Debug;
		};
		846F43B52E39352A0037587F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.alian.travel-withUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "travel with";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		846F43852E3935240037587F /* Build configuration list for PBXProject "travel with" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				846F43AB2E39352A0037587F /* Debug */,
				846F43AC2E39352A0037587F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		846F43AD2E39352A0037587F /* Build configuration list for PBXNativeTarget "travel with" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				846F43AE2E39352A0037587F /* Debug */,
				846F43AF2E39352A0037587F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		846F43B02E39352A0037587F /* Build configuration list for PBXNativeTarget "travel withTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				846F43B12E39352A0037587F /* Debug */,
				846F43B22E39352A0037587F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		846F43B32E39352A0037587F /* Build configuration list for PBXNativeTarget "travel withUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				846F43B42E39352A0037587F /* Debug */,
				846F43B52E39352A0037587F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 846F43822E3935240037587F /* Project object */;
}
