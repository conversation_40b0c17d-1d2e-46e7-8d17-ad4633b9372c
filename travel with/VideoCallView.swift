//
//  VideoCallView.swift
//  travel with
//
//  Created by AI Assistant on 2025/8/1.
//

import SwiftUI
import AVFoundation

// MARK: - 对话消息数据模型
struct ConversationMessage: Identifiable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp = Date()
}

struct VideoCallView: View {
    @EnvironmentObject var aiService: AIService
    @Environment(\.dismiss) private var dismiss
    @StateObject private var cameraManager = CameraManager()
    @State private var conversationMessages: [ConversationMessage] = []
    @State private var currentDisplayingMessage: ConversationMessage?
    @State private var isDisplayingMessage = false
    @State private var messageDisplayTimer: Timer?
    @State private var isRecording = false
    @State private var isMuted = false
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 上部分：摄像头拍摄区域 (9:16比例)
                cameraPreviewSection(geometry: geometry)
                
                // 下部分：AI角色形象显示区域
                aiCharacterSection(geometry: geometry)
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            setupVideoCall()
        }
        .onDisappear {
            cleanupVideoCall()
        }
    }
    
    // MARK: - 摄像头预览区域
    @ViewBuilder
    private func cameraPreviewSection(geometry: GeometryProxy) -> some View {
        let containerWidth = geometry.size.width * 0.95 // 容器宽度为屏幕宽度的95%
        let containerHeight = containerWidth * (3.0 / 4.0) // 横屏显示，宽高比4:3

        VStack(spacing: 20) {
            // 顶部控制按钮
            HStack {
                // 返回按钮
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title)
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(Color.black.opacity(0.4))
                        .clipShape(Circle())
                }

                Spacer()

                // 通话状态
                HStack(spacing: 4) {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 8, height: 8)
                        .scaleEffect(1.5)
                        .animation(.easeInOut(duration: 1.0).repeatForever(), value: true)

                    Text("视频通话中")
                        .font(.caption)
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.black.opacity(0.4))
                .cornerRadius(15)

                Spacer()

                // 摄像头切换按钮
                Button(action: {
                    cameraManager.switchCamera()
                }) {
                    Image(systemName: "camera.rotate")
                        .font(.title)
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(Color.black.opacity(0.4))
                        .clipShape(Circle())
                }
            }
            .padding(.horizontal, 25)
            .padding(.top, 60) // 状态栏高度

            // 摄像头预览容器
            ZStack {
                // 容器背景和边框
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black)
                    .stroke(Color.white.opacity(0.3), lineWidth: 2)
                    .frame(width: containerWidth, height: containerHeight)

                // 摄像头预览（横屏16:9显示，中心裁剪）
                CameraPreviewView(cameraManager: cameraManager)
                    .aspectRatio(contentMode: .fill) // 填充模式，会进行裁剪
                    .frame(width: containerWidth - 4, height: containerHeight - 4)
                    .clipShape(RoundedRectangle(cornerRadius: 18))
            }

            Spacer()
        }
        .frame(height: geometry.size.height * 0.5) // 摄像头区域占屏幕高度的50%
        .background(
            LinearGradient(
                colors: [.black.opacity(0.8), .black.opacity(0.6)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    // MARK: - AI角色形象显示区域
    @ViewBuilder
    private func aiCharacterSection(geometry: GeometryProxy) -> some View {
        let cameraHeight = geometry.size.width * (16.0 / 9.0)
        let remainingHeight = geometry.size.height - cameraHeight
        
        ZStack {
            // 背景渐变
            LinearGradient(
                colors: [.black.opacity(0.8), .black.opacity(0.6)],
                startPoint: .top,
                endPoint: .bottom
            )
            
            VStack(spacing: 0) {
                // 对话栏区域（位于AI形象区域的偏上部位）
                conversationArea
                    .frame(height: remainingHeight * 0.4)
                    .padding(.top, 20)
                
                // AI形象显示区域（暂时留空）
                aiAvatarArea
                    .frame(height: remainingHeight * 0.6)
            }
        }
        .frame(height: remainingHeight)
    }
    
    // MARK: - 对话栏区域
    @ViewBuilder
    private var conversationArea: some View {
        VStack(spacing: 12) {
            // 对话标题
            HStack {
                Image(systemName: "message.circle.fill")
                    .foregroundColor(.blue)
                Text("实时对话")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
            }
            .padding(.horizontal, 20)
            
            // 对话消息显示
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(conversationMessages) { message in
                            ConversationBubbleView(message: message)
                                .id(message.id)
                        }
                        
                        // 当前正在显示的消息
                        if let currentMessage = currentDisplayingMessage {
                            ConversationBubbleView(message: currentMessage, isAnimating: isDisplayingMessage)
                                .id(currentMessage.id)
                        }
                    }
                    .padding(.horizontal, 20)
                }
                .onChange(of: conversationMessages.count) {
                    // 自动滚动到最新消息
                    if let lastMessage = conversationMessages.last {
                        withAnimation(.easeOut(duration: 0.3)) {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }
                    }
                }
                .onChange(of: currentDisplayingMessage?.id) {
                    // 滚动到当前显示的消息
                    if let currentMessage = currentDisplayingMessage {
                        withAnimation(.easeOut(duration: 0.3)) {
                            proxy.scrollTo(currentMessage.id, anchor: .bottom)
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - AI形象区域（暂时留空）
    @ViewBuilder
    private var aiAvatarArea: some View {
        VStack {
            Spacer()
            
            // 占位符
            VStack(spacing: 16) {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 80))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.orange, .pink],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                Text("AI朋友形象")
                    .font(.title2)
                    .foregroundColor(.white.opacity(0.7))
                
                Text("即将上线")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.5))
            }
            
            Spacer()
        }
    }
    
    // MARK: - 设置视频通话
    private func setupVideoCall() {
        cameraManager.startSession()
        
        // 模拟AI欢迎消息
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            displayMessage("你好！很高兴和你视频通话！", isFromUser: false)
        }
        
        // 模拟用户消息
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            displayMessage("你好AI朋友，今天天气真不错！", isFromUser: true)
        }
        
        // 模拟AI回复
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            displayMessage("是的呢！看起来是个适合出门旅行的好天气。你有什么计划吗？", isFromUser: false)
        }
    }
    
    // MARK: - 清理视频通话
    private func cleanupVideoCall() {
        cameraManager.stopSession()
        messageDisplayTimer?.invalidate()
        messageDisplayTimer = nil
    }

    // MARK: - 开始录音
    private func startRecording() {
        isRecording = true
        print("🎤 开始语音识别...")

        Task {
            await aiService.startVoiceRecognition()
        }
    }

    // MARK: - 停止录音
    private func stopRecording() {
        isRecording = false
        print("🛑 停止语音识别...")

        aiService.stopVoiceRecognition()

        // 模拟获取识别结果并显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 这里应该从语音识别服务获取结果，暂时模拟
            let recognizedText = "刚才说的话" // 实际应该从ASR服务获取
            if !recognizedText.isEmpty {
                displayMessage(recognizedText, isFromUser: true)

                // 发送给AI并获取回复
                Task {
                    await sendMessageToAI(recognizedText)
                }
            }
        }
    }

    // MARK: - 发送消息给AI
    private func sendMessageToAI(_ text: String) async {
        // 发送消息给AI服务
        await aiService.sendTextMessage(text)

        // 获取AI回复并显示
        if let lastMessage = aiService.messages.last, !lastMessage.isFromUser {
            DispatchQueue.main.async {
                displayMessage(lastMessage.content, isFromUser: false)
            }
        }
    }
    
    // MARK: - 显示消息（按句子逐一显示）
    private func displayMessage(_ text: String, isFromUser: Bool) {
        let sentences = splitIntoSentences(text)
        displaySentences(sentences, isFromUser: isFromUser, index: 0)
    }
    
    // MARK: - 分句显示
    private func displaySentences(_ sentences: [String], isFromUser: Bool, index: Int) {
        guard index < sentences.count else { return }

        let sentence = sentences[index].trimmingCharacters(in: .whitespacesAndNewlines)
        guard !sentence.isEmpty else {
            // 跳过空句子
            displaySentences(sentences, isFromUser: isFromUser, index: index + 1)
            return
        }

        let message = ConversationMessage(content: sentence, isFromUser: isFromUser)

        // 开始显示动画
        currentDisplayingMessage = message
        isDisplayingMessage = true

        // 简单的显示效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeInOut(duration: 0.5)) {
                isDisplayingMessage = false
            }
        }

        // 显示完成后添加到消息列表
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            conversationMessages.append(message)
            currentDisplayingMessage = nil

            // 继续显示下一句
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                displaySentences(sentences, isFromUser: isFromUser, index: index + 1)
            }
        }
    }
    
    // MARK: - 分句处理
    private func splitIntoSentences(_ text: String) -> [String] {
        // 按照中文和英文的句号、问号、感叹号分句
        let sentences = text.components(separatedBy: CharacterSet(charactersIn: "。！？.!?"))
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        
        return sentences.isEmpty ? [text] : sentences
    }
}

// MARK: - 对话气泡视图
struct ConversationBubbleView: View {
    let message: ConversationMessage
    var isAnimating: Bool = false

    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer(minLength: 50)
                messageContent
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(LinearGradient(
                                colors: [.blue, .blue.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                    )
            } else {
                HStack(alignment: .top, spacing: 8) {
                    // AI头像
                    Image(systemName: "heart.circle.fill")
                        .font(.title3)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.orange, .pink],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )

                    messageContent
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.white.opacity(0.9))
                                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                        )
                }
                Spacer(minLength: 50)
            }
        }
        .scaleEffect(isAnimating ? 0.95 : 1.0)
        .opacity(isAnimating ? 0.7 : 1.0)
        .animation(.easeInOut(duration: 0.3), value: isAnimating)
    }

    @ViewBuilder
    private var messageContent: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(message.content)
                .font(.body)
                .foregroundColor(message.isFromUser ? .white : .primary)

            // 时间戳
            Text(message.timestamp, style: .time)
                .font(.caption2)
                .foregroundColor(message.isFromUser ? .white.opacity(0.7) : .secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
    }
}
