//
//  AIChatView.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI
import PhotosUI

struct AIChatView: View {
    @EnvironmentObject var aiService: AIService
    @State private var messageText = ""
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var showingEmojiPicker = false
    @State private var hasShownWelcome = false
    @State private var isVoiceMode = false
    @State private var showingMoreOptions = false
    @State private var isRecording = false
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 消息列表
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            // 加载更多历史记录按钮
                            if let historyManager = aiService.historyManager, historyManager.hasMoreHistory {
                                Button(action: {
                                    Task {
                                        // 记录当前第一条消息的ID，用于加载后定位
                                        let firstMessageId = aiService.messages.first?.id
                                        await aiService.loadMoreHistory()

                                        // 加载完成后，滚动到之前的第一条消息位置
                                        if let firstId = firstMessageId {
                                            withAnimation(.easeOut(duration: 0.3)) {
                                                proxy.scrollTo(firstId, anchor: .top)
                                            }
                                        }
                                    }
                                }) {
                                    HStack {
                                        if historyManager.isLoading {
                                            ProgressView()
                                                .scaleEffect(0.8)
                                        }
                                        Text(historyManager.isLoading ? "加载中..." : "查看更多历史消息")
                                            .font(.caption)
                                            .foregroundColor(.blue)
                                    }
                                    .padding(.vertical, 8)
                                    .padding(.horizontal, 16)
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(20)
                                }
                                .padding(.horizontal)
                                .padding(.bottom, 8)
                            }

                            ForEach(aiService.messages) { message in
                                MessageBubbleView(message: message)
                                    .id(message.id)
                            }

                            // 加载指示器
                            if aiService.isLoading {
                                HStack {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("AI朋友正在思考...")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Spacer()
                                }
                                .padding(.horizontal)
                            }
                        }
                        .padding(.horizontal)
                        .padding(.top)
                    }
                    .onChange(of: aiService.messages.count) {
                        // 自动滚动到最新消息
                        if let lastMessage = aiService.messages.last {
                            withAnimation(.easeOut(duration: 0.3)) {
                                proxy.scrollTo(lastMessage.id, anchor: .bottom)
                            }
                        }
                    }
                }
                
                // 输入栏
                ChatInputView(
                    messageText: $messageText,
                    selectedPhoto: $selectedPhoto,
                    showingEmojiPicker: $showingEmojiPicker,
                    isVoiceMode: $isVoiceMode,
                    showingMoreOptions: $showingMoreOptions,
                    isRecording: $isRecording,
                    isTextFieldFocused: $isTextFieldFocused,
                    onSendMessage: sendMessage,
                    onSendPhoto: sendPhoto,
                    onSendEmoji: sendEmoji,
                    onStartRecording: startRecording,
                    onStopRecording: stopRecording,
                    onVideoCall: startVideoCall
                )
            }
            .navigationTitle("聊天")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    ConnectionStatusView(status: aiService.connectionStatus)
                }
            }
            .onTapGesture {
                isTextFieldFocused = false
                showingEmojiPicker = false
            }
            .onAppear {
                // 首次进入聊天页面时显示欢迎消息
                if !hasShownWelcome && aiService.messages.isEmpty {
                    hasShownWelcome = true
                    Task {
                        await showWelcomeMessage()
                    }
                }
            }
        }
    }

    // MARK: - 显示欢迎消息
    private func showWelcomeMessage() async {
        let welcomeMessage = ChatMessage(
            content: "你好！我是你的AI旅行伙伴，很高兴认识你！今天想去哪里玩呢？🌟",
            isFromUser: false,
            messageType: .text
        )
        aiService.messages.append(welcomeMessage)
    }
    
    // MARK: - 发送消息
    private func sendMessage() {
        guard !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let text = messageText
        messageText = ""
        isTextFieldFocused = false
        
        Task {
            await aiService.sendTextMessage(text)
        }
    }
    
    // MARK: - 发送图片
    private func sendPhoto() {
        guard let selectedPhoto = selectedPhoto else { return }
        
        Task {
            if let data = try? await selectedPhoto.loadTransferable(type: Data.self),
               let image = UIImage(data: data) {
                await aiService.sendImageMessage(image)
            }
        }
        
        self.selectedPhoto = nil
    }
    
    // MARK: - 发送表情
    private func sendEmoji(_ emoji: String) {
        Task {
            await aiService.sendEmojiMessage(emoji)
        }
        showingEmojiPicker = false
    }

    // MARK: - 开始录音
    private func startRecording() {
        isRecording = true
        print("🎤 开始语音识别...")

        Task {
            await aiService.startVoiceRecognition()
        }
    }

    // MARK: - 停止录音
    private func stopRecording() {
        isRecording = false
        print("🛑 停止语音识别...")

        aiService.stopVoiceRecognition()
    }

    // MARK: - 视频通话
    private func startVideoCall() {
        showingMoreOptions = false
        // TODO: 实现视频通话功能
        print("开始视频通话...")
    }
}

// MARK: - 消息气泡组件
struct MessageBubbleView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer(minLength: 50)
                messageContent
                    .background(
                        RoundedRectangle(cornerRadius: 18)
                            .fill(LinearGradient(
                                colors: [.blue, .blue.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                    )
            } else {
                HStack(alignment: .top, spacing: 8) {
                    // AI头像
                    Image(systemName: "heart.circle.fill")
                        .font(.title2)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.orange, .pink],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                    
                    messageContent
                        .background(
                            RoundedRectangle(cornerRadius: 18)
                                .fill(Color(.systemGray6))
                                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                        )
                }
                Spacer(minLength: 50)
            }
        }
    }
    
    @ViewBuilder
    private var messageContent: some View {
        VStack(alignment: .leading, spacing: 4) {
            if message.messageType == .image, let imageData = message.imageData,
               let image = UIImage(data: imageData) {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: 200, maxHeight: 200)
                    .cornerRadius(12)
                
                if !message.content.isEmpty {
                    Text(message.content)
                        .font(.body)
                        .foregroundColor(message.isFromUser ? .white : .primary)
                }
            } else {
                Text(message.content)
                    .font(.body)
                    .foregroundColor(message.isFromUser ? .white : .primary)
            }
            
            // 时间戳
            Text(message.timestamp, style: .time)
                .font(.caption2)
                .foregroundColor(message.isFromUser ? .white.opacity(0.7) : .secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
    }
}

// MARK: - 输入栏组件
struct ChatInputView: View {
    @Binding var messageText: String
    @Binding var selectedPhoto: PhotosPickerItem?
    @Binding var showingEmojiPicker: Bool
    @Binding var isVoiceMode: Bool
    @Binding var showingMoreOptions: Bool
    @Binding var isRecording: Bool
    var isTextFieldFocused: FocusState<Bool>.Binding

    let onSendMessage: () -> Void
    let onSendPhoto: () -> Void
    let onSendEmoji: (String) -> Void
    let onStartRecording: () -> Void
    let onStopRecording: () -> Void
    let onVideoCall: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            Divider()

            // 输入栏
            HStack(spacing: 12) {
                // 语音/文本切换按钮
                Button(action: {
                    withAnimation(.spring()) {
                        isVoiceMode.toggle()
                        if isVoiceMode {
                            isTextFieldFocused.wrappedValue = false
                            showingEmojiPicker = false
                        }
                    }
                }) {
                    Image(systemName: isVoiceMode ? "keyboard" : "mic.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                }

                // 输入区域
                if isVoiceMode {
                    // 录音按钮
                    Button(action: {}) {
                        HStack {
                            if isRecording {
                                // 录音中显示动画和识别状态
                                HStack(spacing: 8) {
                                    Circle()
                                        .fill(Color.red)
                                        .frame(width: 8, height: 8)
                                        .scaleEffect(isRecording ? 1.5 : 1.0)
                                        .animation(.easeInOut(duration: 0.5).repeatForever(), value: isRecording)

                                    Text("正在识别...")
                                        .font(.body)
                                        .foregroundColor(.white)
                                }
                            } else {
                                Text("按住说话")
                                    .font(.body)
                                    .foregroundColor(.white)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(isRecording ? Color.red : Color.blue)
                        )
                    }
                    .scaleEffect(isRecording ? 1.05 : 1.0)
                    .animation(.easeInOut(duration: 0.1), value: isRecording)
                    .simultaneousGesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { _ in
                                if !isRecording {
                                    onStartRecording()
                                }
                            }
                            .onEnded { _ in
                                if isRecording {
                                    onStopRecording()
                                }
                            }
                    )

                    // 表情按钮（语音模式下也显示）
                    Button(action: {
                        withAnimation(.spring()) {
                            showingEmojiPicker.toggle()
                            showingMoreOptions = false
                        }
                    }) {
                        Image(systemName: showingEmojiPicker ? "face.smiling.inverse" : "face.smiling")
                            .font(.title2)
                            .foregroundColor(.orange)
                    }
                } else {
                    // 文本输入框
                    TextField("说点什么...", text: $messageText, axis: .vertical)
                        .textFieldStyle(.roundedBorder)
                        .focused(isTextFieldFocused)
                        .lineLimit(1...4)

                    // 表情按钮
                    Button(action: {
                        withAnimation(.spring()) {
                            showingEmojiPicker.toggle()
                            isTextFieldFocused.wrappedValue = false
                            showingMoreOptions = false
                        }
                    }) {
                        Image(systemName: showingEmojiPicker ? "face.smiling.inverse" : "face.smiling")
                            .font(.title2)
                            .foregroundColor(.orange)
                    }
                }

                // 发送/更多选项按钮
                if isVoiceMode || messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    // 更多选项按钮（圆形加号）
                    Button(action: {
                        withAnimation(.spring()) {
                            showingMoreOptions.toggle()
                            isTextFieldFocused.wrappedValue = false
                            showingEmojiPicker = false
                        }
                    }) {
                        Image(systemName: showingMoreOptions ? "xmark.circle.fill" : "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                } else {
                    // 发送按钮
                    Button(action: onSendMessage) {
                        Image(systemName: "paperplane.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
            .background(Color(.systemBackground))

            // 表情包选择器（位于输入栏下方）
            if showingEmojiPicker {
                EmojiPickerView(onEmojiSelected: onSendEmoji)
                    .transition(.move(edge: .bottom))
            }

            // 更多选项面板（位于输入栏下方）
            if showingMoreOptions {
                MoreOptionsView(
                    selectedPhoto: $selectedPhoto,
                    onSendPhoto: onSendPhoto,
                    onVideoCall: onVideoCall,
                    onDismiss: {
                        withAnimation(.spring()) {
                            showingMoreOptions = false
                        }
                    }
                )
                .transition(.move(edge: .bottom).combined(with: .opacity))
            }
        }
    }
}

// MARK: - 更多选项面板
struct MoreOptionsView: View {
    @Binding var selectedPhoto: PhotosPickerItem?
    let onSendPhoto: () -> Void
    let onVideoCall: () -> Void
    let onDismiss: () -> Void

    var body: some View {
        HStack(spacing: 50) {
            Spacer()

            // 图片选择按钮
            VStack(spacing: 8) {
                PhotosPicker(selection: $selectedPhoto, matching: .images) {
                    Image(systemName: "photo.circle.fill")
                        .font(.largeTitle)
                        .foregroundColor(.blue)
                }
                .onChange(of: selectedPhoto) {
                    if selectedPhoto != nil {
                        onSendPhoto()
                        onDismiss()
                    }
                }

                Text("图像")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // 视频通话按钮
            VStack(spacing: 8) {
                Button(action: {
                    onVideoCall()
                }) {
                    Image(systemName: "video.circle.fill")
                        .font(.largeTitle)
                        .foregroundColor(.green)
                }

                Text("视频通话")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(.vertical, 20)
        .background(Color(.systemGray6))
    }
}

// MARK: - 表情包选择器
struct EmojiPickerView: View {
    let onEmojiSelected: (String) -> Void
    
    private let emojis = [
        "😊", "😂", "🥰", "😍", "🤗", "😘", "😋", "😎",
        "🤔", "😮", "😱", "😴", "🤤", "😇", "🥳", "🤩",
        "👍", "👎", "👌", "✌️", "🤞", "🤟", "👏", "🙌",
        "❤️", "💕", "💖", "💗", "💝", "💘", "💞", "💓",
        "🌟", "⭐", "✨", "💫", "🌈", "🌸", "🌺", "🌻",
        "🎉", "🎊", "🎈", "🎁", "🎂", "🍰", "🧁", "🍭"
    ]
    
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 8), spacing: 15) {
            ForEach(emojis, id: \.self) { emoji in
                Button(action: {
                    onEmojiSelected(emoji)
                }) {
                    Text(emoji)
                        .font(.title2)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }
}

// MARK: - 连接状态指示器
struct ConnectionStatusView: View {
    let status: AIService.ConnectionStatus
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(statusColor)
                .frame(width: 8, height: 8)
            Text(statusText)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private var statusColor: Color {
        switch status {
        case .connected: return .green
        case .connecting: return .orange
        case .disconnected, .error: return .red
        }
    }
    
    private var statusText: String {
        switch status {
        case .connected: return "在线"
        case .connecting: return "连接中"
        case .disconnected: return "离线"
        case .error: return "错误"
        }
    }
}

// MARK: - 预览
#Preview {
    AIChatView()
        .environmentObject(AIService.preview)
}
