# Travel With - AI旅行陪伴应用

## 项目概述
Travel With是一款以AI朋友为核心的温暖旅行陪伴应用，主要解决独自旅行时缺乏互动分享的孤独感。应用的核心是一个亲切友好的AI朋友，能够像真正的朋友一样陪伴用户旅行，提供情感支持、智能建议和实时互动。地图导航和其他功能都是为了增强这种陪伴体验而设计的辅助功能。

## 目标用户
- **独自旅行者**：希望在旅行中有人分享和互动，减少孤独感
- **懒人出行团体**：不想花时间做攻略，希望有智能助手帮助规划
- **寻求温暖陪伴的用户**：希望有一个贴心的AI朋友一起探索世界

## 技术选型
- 开发框架: SwiftUI + UIKit混合开发
- 数据持久化: SwiftData
- 状态管理: Combine + SwiftUI原生状态管理
- 地图服务: MapKit + 第三方地图API
- AI集成: 双模态大模型架构（角色扮演模型 + 助手模型）
- 网络请求: URLSession + async/await
- UI风格: 遵循iOS Human Interface Guidelines (HIG)，采用温暖舒适的亮色系设计，让用户感到开心愉悦

## 应用结构
```
Travel With App (以AI朋友为核心)
├── 主导航模块 (4个Tab: 地图、地点、聊天、我的)
├── AI朋友系统 (核心功能，专注聊天体验)
│   ├── 聊天界面 (类似微信，支持表情包、识图)
│   └── 智能对话 (角色扮演，记忆管理)
├── 地图导航系统 (传统导航，多种交通方式)
├── 地点管理系统 (收藏地点，旅行计划)
├── 个人中心 (设置、历史、配置集成)
└── 数据管理系统 (本地存储 + 云同步)
```

## 页面结构

| 页面/视图名称 | 用途 | 核心功能 | 技术实现 | 导航/用户流程 | 建议文件路径 |
|:--------:|:----:|:--------:|:--------:|:--------:|:--------:|
| 主界面 | 四Tab导航容器 | Tab导航(地图、地点、聊天、我的)、AI状态指示 | SwiftUI TabView、自定义Tab设计、聊天Tab高亮 | 应用启动后直接进入，聊天Tab为默认焦点 | `travel with/MainTabView.swift` |
| 聊天页面 | 核心AI朋友交流界面 | 类微信聊天、表情包发送、图片识别、语音录音、视频通话 | SwiftUI + Combine、现代聊天UI设计、多媒体处理 | 应用启动默认页面，AI主动欢迎用户 | `travel with/AIChatView.swift` |
| 地点管理页面 | 地点收藏和计划管理 | 地点搜索添加、旅行计划创建、地点卡片展示、详情查看 | SwiftUI + SwiftData、MapKit搜索、卡片式设计 | 从Tab进入，管理所有收藏地点和旅行计划 | `travel with/PlacesView.swift` |
| 地图导航页面 | 传统地图导航+地点显示 | 实时定位、路线规划、地点标注显示、计划切换、多种交通方式 | MapKit、第三方应用集成、地点标注 | 从Tab进入，显示选中计划的地点，支持多种导航方式 | `travel with/MapNavigationView.swift` |
| 个人中心页面 | 用户设置和功能集成 | AI配置、个人设置、旅行历史、攻略管理 | SwiftUI表单、数据管理、功能整合 | 从Tab进入，集成所有个人相关功能 | `travel with/ProfileView.swift` |
| API配置页面 | AI模型连接设置 | API密钥配置、模型选择、连接测试、角色设定 | 安全存储、网络测试、表单验证 | 从个人中心进入，首次使用必须配置 | `travel with/APIConfigView.swift` |
| 启动欢迎页面 | 首次启动和AI连接 | 权限申请、API连接、AI首次问候、快速上手 | SwiftUI动画、权限框架、AI初始化 | 首次启动显示，连接成功后AI主动问候 | `travel with/WelcomeView.swift` |

## 内置AI模型配置
```swift
// 豆包API配置 (内置，无需用户配置)
- API密钥: aeaa5a81-6333-4a78-83d2-418230bbf85f
- 基础URL: https://ark.cn-beijing.volces.com/api/v3/chat/completions
- 角色扮演模型: ep-20250730103733-kzl7l (AI朋友聊天)
- 专用图像识别模型: ep-20250730213933-4s55h (图像识别)
```

## 数据模型
```swift
// 核心数据模型 (以AI朋友为中心)
- ChatMessage: AI对话消息（文本、表情包、图片、语音）
- Trip: 旅行记录（路线、时间、导航记录）
- UserProfile: 用户信息和偏好设置
- TravelPlan: 旅行计划（名称、创建时间、包含的地点列表）
- Place: 地点信息（名称、地址、坐标、封面图、备注、所属计划）
```

## 核心设计理念
- **AI朋友优先**：应用启动直接进入主界面，AI后台连接
- **温暖舒适UI**：亮色系设计，让用户感到开心愉悦
- **简洁三Tab**：地图、聊天(核心+高亮)、我的
- **无缝陪伴**：AI在所有页面都能互动，真正的朋友体验
- **微信式交互**：熟悉的聊天界面，支持表情包和多媒体
- **即开即用**：无需复杂的引导流程，直接开始使用

## 技术实现细节

### AI服务管理器

#### AI服务架构设计
- 内置豆包API配置，用户打开应用即可使用
- **三模型协同架构**：角色扮演模型(AI朋友) + 多模态助手模型(图像识别+助手) + 专用图像识别模型(高精度识别)
- **双模型图像处理流程**：专用识别模型 → 角色扮演模型，提供更准确和友好的图像分析
- 统一的消息处理接口，支持文本、图像、语音等多种输入
- 智能模型选择：根据消息类型和任务需求自动选择最适合的AI模型
- **智能回退机制**：专用模型失败时自动回退到备用多模态模型

#### 网络请求方案
- 使用URLSession + async/await实现现代异步网络请求
- 支持豆包API的标准HTTP请求格式
- 完善的错误处理：网络超时、API错误、解析错误等
- 自动重试机制和请求队列管理

#### 数据管理方案
- ChatMessage模型支持多种消息类型(文本、图片、表情包)
- **双模型处理流程**：图像消息先经过专用识别模型，再由角色扮演模型生成回复
- **自然对话风格**：系统提示词优化，让AI回复更像朋友间的日常聊天
- 会话上下文管理，保持AI对话的连贯性
- 本地消息缓存，提升用户体验
- 实时消息状态更新(发送中、已发送、失败等)
- **图像识别结果缓存**：避免重复识别相同图片

#### 安全性考虑
- API密钥通过代码内置，避免用户配置复杂性
- HTTPS加密传输，保护数据安全
- 图像数据压缩和隐私保护
- 敏感信息本地加密存储
- 语音数据实时处理，不存储音频文件
- WebSocket安全连接，支持签名认证

### ASR语音识别服务

#### ASR服务架构设计
- **双重识别引擎**：火山引擎ASR作为主要方案，iOS原生Speech框架作为备选
- **智能切换机制**：默认使用火山引擎ASR，可根据需要切换到iOS原生识别
- **实时语音处理**：支持实时音频流处理和WebSocket连接
- **权限管理优化**：自动申请和管理麦克风、语音识别权限
- **错误处理完善**：多层错误处理机制，确保服务稳定性

#### 火山引擎ASR集成
- **WebSocket连接**：实现与火山引擎ASR服务的实时WebSocket连接
- **签名认证**：实现HMAC-SHA256签名认证机制
- **音频流传输**：支持PCM音频流的实时传输和处理
- **配置参数**：采样率16kHz，16bit位深，单声道PCM格式
- **实时识别**：支持部分结果和最终结果的实时回调

#### iOS原生语音识别
- **Speech框架集成**：使用iOS原生SFSpeechRecognizer进行语音识别
- **离线支持**：支持离线语音识别，无需网络连接
- **多语言支持**：主要支持中文，可扩展其他语言
- **实时反馈**：支持实时部分结果和最终结果回调
- **权限处理**：自动处理语音识别权限申请和状态管理

#### 音频处理方案
- **AVAudioEngine集成**：使用AVAudioEngine进行音频采集和处理
- **音频会话优化**：支持蓝牙耳机、外放等多种音频模式
- **实时音频流**：支持实时音频缓冲区处理和传输
- **格式转换**：自动处理音频格式转换和采样率调整
- **噪音处理**：基础的音频质量优化和噪音抑制

#### 状态管理方案
- **录音状态管理**：完整的录音开始、进行中、停止状态管理
- **识别状态反馈**：实时显示识别状态和进度
- **错误状态处理**：完善的错误状态管理和用户提示
- **权限状态监控**：实时监控麦克风和语音识别权限状态
- **连接状态管理**：WebSocket连接状态的实时监控和管理

### 主界面

#### UI设计方案
- 采用SwiftUI TabView作为主导航，三Tab结构：地图、聊天(核心)、我的
- 聊天Tab位于中间并高亮显示，使用特殊图标和颜色强调
- 温暖亮色系设计：主色调采用温暖的蓝色和橙色渐变
- 自定义Tab样式，圆角设计，符合现代iOS美学

#### 数据管理方案
- @StateObject管理全局AIService实例
- @State管理当前选中Tab状态
- 环境对象传递，确保所有子页面都能访问AI服务

#### 交互实现
- 流畅的Tab切换动画和视觉反馈
- AI连接状态实时显示在导航栏
- 聊天Tab新消息红点提醒机制

#### iOS特性利用
- SF Symbols系统图标保持一致性
- Dynamic Type支持，适应用户字体偏好
- 安全区域适配，支持所有iPhone型号

#### 可访问性考虑
- 完整的VoiceOver支持和导航
- 高对比度颜色确保可读性
- 语义化的无障碍标签

#### 组件复用
- 可复用的自定义Tab样式组件
- 全局AIService环境对象管理

### 聊天页面

#### UI设计方案
- 现代聊天界面：消息气泡、时间戳、AI头像设计
- 用户消息右侧蓝色渐变气泡，AI消息左侧灰色气泡带阴影
- 智能输入栏：语音/文本切换、录音按钮、更多选项面板
- 支持多种消息类型：文本、图片、表情包、语音消息的统一显示
- 类微信交互：按住录音、圆形加号按钮、弹出选项面板

#### 数据管理方案
- @EnvironmentObject获取全局AIService实例
- @State管理输入文本、语音模式、录音状态、更多选项显示
- 实时消息列表绑定和自动滚动机制
- PhotosPicker集成图片选择功能
- 录音状态和语音识别数据管理

#### 交互实现
- 流畅的消息发送动画和状态反馈
- 自动滚动到最新消息位置
- 语音/文本模式无缝切换
- 按住录音交互，松开发送语音
- 更多选项面板弹出动画
- 图片和视频通话快速访问

#### iOS特性利用
- PhotosPicker现代图片选择体验
- 键盘避让和输入栏自适应
- 触觉反馈增强交互体验
- 系统分享和复制功能
- 语音录制和识别集成（待实现）
- 视频通话功能集成（待实现）

#### 可访问性考虑
- VoiceOver完整的消息朗读支持
- Dynamic Type自适应字体大小
- 高对比度模式适配
- 语义化的消息和控件标签
- 录音按钮的无障碍支持

#### 组件复用
- MessageBubbleView可复用消息气泡组件
- ChatInputView智能输入栏组件（集成语音识别）
- MoreOptionsView更多选项面板组件
- EmojiPickerView表情选择器组件
- ASRService语音识别服务组件

#### ASR语音识别集成
- **双重识别引擎**：iOS原生Speech框架 + 火山引擎ASR服务
- **智能切换机制**：默认使用iOS原生，可切换到火山引擎ASR
- **实时语音转文字**：支持实时语音识别和文字转换
- **自动发送机制**：识别完成后自动发送文字给AI朋友
- **权限管理**：自动申请麦克风和语音识别权限
- **错误处理**：完善的错误处理和回退机制
- **状态可视化**：录音状态动画和实时反馈

### 地图导航页面

#### UI设计方案
- MapKit交互式地图作为主体，支持手势操作和缩放
- 实时定位显示用户当前位置和移动轨迹
- AI弹窗提醒系统：类微信样式的智能提醒弹窗
- 顶部搜索栏：地点搜索和POI查找功能
- 底部控制栏：路线规划、导航控制、定位功能
- **多种交通方式选择**：开车、步行、公交、骑车、打车五种方式
- **第三方应用集成**：支持跳转到滴滴、高德、百度、腾讯、Apple地图

#### 数据管理方案
- @StateObject管理LocationManager实现位置服务
- @State管理地图区域、搜索结果、选中地点
- Core Location集成：实时定位、权限管理、后台定位
- MapKit路线规划：多种交通方式、实时路况
- **路线选项管理**：支持多种交通方式的路线计算和比较
- **第三方应用URL Scheme**：集成主流地图和打车应用

#### 交互实现
- 流畅的地图交互：拖拽、缩放、旋转手势
- 智能搜索建议和自动完成功能
- AI弹窗智能触发：基于位置、时间、用户行为
- 多种导航方式选择，灵活切换体验
- **交通方式快速切换**：横向滚动选择器，实时显示路线信息
- **打车应用选择**：弹窗展示可用应用，一键跳转

#### iOS特性利用
- Core Location高精度定位和地理围栏
- MapKit原生地图性能和用户体验
- 系统权限优雅申请和管理
- 后台定位和通知推送
- **URL Scheme深度链接**：与第三方应用无缝集成
- **应用可用性检测**：智能检测已安装的应用

#### 可访问性考虑
- VoiceOver完整的地图导航支持
- Dynamic Type大字体模式适配
- 高对比度地图样式选择
- 语音导航和触觉反馈
- **交通方式选择器无障碍支持**：语音朗读和导航

#### 组件复用
- AIPopupView可复用智能弹窗组件
- SearchBar统一搜索栏组件
- LocationManager全局位置服务管理
- **TransportTypeSelector交通方式选择器**：可复用的交通方式选择组件
- **RideOptionsView打车选项弹窗**：第三方应用选择界面

#### 功能完整性检查表
- [x] 基础地图显示和交互
- [x] 用户位置定位和显示
- [x] 地点搜索和结果展示
- [x] 路线规划和显示
- [x] 多种交通方式支持（开车、步行、公交、骑车、打车）
- [x] 交通方式选择器UI
- [x] 路线信息实时显示（距离、时间）
- [x] 第三方打车应用集成
- [x] 应用可用性检测和App Store跳转
- [x] AI弹窗提醒系统
- [x] 传统导航功能完善
- [x] 导航开始/停止控制



### 个人中心页面

#### UI设计方案
- 用户头像和基本信息的精美展示区域
- 分组设置列表：AI配置、旅行历史、个人偏好、应用设置
- 现代卡片式布局，每个功能区域独立卡片设计
- 温暖舒适的配色：渐变背景和圆角卡片设计

#### 数据管理方案
- @State管理导航状态、设置开关、用户信息
- UserDefaults持久化用户偏好和应用设置
- SwiftData查询和管理旅行历史数据
- @EnvironmentObject访问全局AI服务状态

#### 交互实现
- 流畅的列表导航和页面转场动画
- 设置项的即时反馈和实时状态更新
- 数据清理确认对话框和重置功能
- 系统分享框架集成导出功能

#### iOS特性利用
- 系统原生设置样式的列表设计语言
- SF Symbols丰富的图标系统
- 系统分享框架和活动视图控制器
- 用户通知权限和设置管理

#### 可访问性考虑
- VoiceOver完整的导航和内容朗读
- Dynamic Type大字体模式完全适配
- 高对比度模式和颜色适配
- 语义化的设置项标签和描述

#### 组件复用
- SettingsRow可复用的设置行组件
- ProfileHeader统一的用户信息头部
- ActionSheet可复用的操作确认表单

### 地点管理页面

#### UI设计方案
- 顶部搜索栏：MapKit地点搜索，实时显示搜索结果，右侧添加按钮
- 计划导航栏：横向滚动的计划标签，支持切换和新建计划
- 地点卡片列表：封面图+地点信息的卡片式设计，支持点击查看详情
- 空状态提示：友好的空状态引导用户添加地点

#### 数据管理方案
- @StateObject管理PlaceManager实例，处理地点和计划数据
- @State管理搜索状态、UI交互状态、选中地点
- SwiftData持久化存储旅行计划和地点数据
- MKLocalSearch集成地点搜索功能

#### 交互实现
- 实时搜索建议和地点添加流程
- 计划标签的流畅切换和创建体验
- 地点卡片的点击查看和长按删除
- 地点详情页面的模态展示

#### iOS特性利用
- MapKit地点搜索和地理编码服务
- SwiftData现代数据持久化框架
- AsyncImage异步图片加载和缓存
- 系统原生的搜索和列表交互

#### 可访问性考虑
- VoiceOver完整的搜索和列表导航支持
- Dynamic Type自适应字体大小
- 高对比度模式适配
- 语义化的地点信息标签

#### 组件复用
- SearchBarView可复用搜索栏组件
- PlaceCardView统一地点卡片组件
- PlanNavigationView计划导航组件
- EmptyPlacesView空状态提示组件

#### 功能完整性检查表
- [x] 地点搜索和实时结果显示
- [x] 地点添加到当前计划
- [x] 旅行计划创建和管理
- [x] 计划切换和地点筛选
- [x] 地点卡片展示和交互
- [x] 地点详情页面（基础版本）
- [x] 长按删除地点功能
- [x] 空状态友好提示
- [x] Unsplash封面图片集成

### 启动欢迎页面

#### UI设计方案
- 温暖欢迎的启动动画：品牌Logo和应用名称展示
- 分步骤权限申请引导：位置、相机、通知权限的友好说明
- AI连接状态实时展示：连接进度和状态反馈
- 流畅的引导流程：渐进式过渡动画和步骤指示

#### 数据管理方案
- @State管理引导步骤、权限状态、连接状态
- UserDefaults持久化首次启动标记和用户选择
- PermissionManager统一管理各种系统权限申请
- @EnvironmentObject监听AI服务连接状态

#### 交互实现
- 分步骤的友好引导流程和进度指示
- 权限申请的详细解释和使用场景说明
- AI连接成功后的个性化欢迎体验
- 完成引导后的自动跳转和状态保存

#### iOS特性利用
- 系统权限申请框架和最佳实践
- 启动画面优化和品牌一致性
- Core Animation流畅动画效果
- 用户体验和引导流程优化

#### 可访问性考虑
- VoiceOver完整的引导流程朗读
- Dynamic Type大字体模式完全适配
- 高对比度模式和颜色无障碍设计
- 语音引导和操作提示

#### 组件复用
- PermissionRequestView统一权限申请组件
- WelcomeStepView可复用引导步骤组件
- AnimatedLogoView品牌动画展示组件

## 开发状态跟踪
| 页面/组件名称 | 开发状态 | 文件路径 |
|:-------------:|:--------:|:--------:|
| AI服务管理器 | 已完成🔥 | `travel with/AIService.swift` |
| ASR语音识别服务 | 已完成🎤 | `travel with/ASRService.swift` |
| 对话历史管理器 | 已完成🆕 | `travel with/ChatHistoryManager.swift` |
| 共享位置管理器 | 已完成🔧 | `travel with/SharedLocationManager.swift` |
| 地点管理器 | 已完成🆕 | `travel with/PlaceManager.swift` |
| 主界面 | 已完成✨ | `travel with/MainTabView.swift` |
| 聊天页面 | 已完成🎨🎤 | `travel with/AIChatView.swift` |
| 对话历史页面 | 已完成🆕 | `travel with/ChatHistoryView.swift` |
| 地点管理页面 | 已完成🆕 | `travel with/PlacesView.swift` |
| 地图导航页面 | 已完成✨🆕 | `travel with/MapNavigationView.swift` |
| 个人中心页面 | 已完成🆕 | `travel with/ProfileView.swift` |
| 启动欢迎页面 | 已完成 | `travel with/WelcomeView.swift` |
| 应用权限配置 | 已完成🎤✅ | `PERMISSION_SETUP_GUIDE.md` |
| 地图导航测试 | 已完成 | `travel with/MapNavigationTests.swift` |
| AI图像识别测试 | 已完成🔥 | `travel withTests/AIImageRecognitionTests.swift` |
| ASR语音识别测试 | 已完成🎤 | `travel withTests/ASRServiceTests.swift` |

## 最新更新 (2025/7/31)

### ASR二进制协议标准化修复 🎤📋✅
- **协议头部标准化**：完全按照火山引擎官方文档修正二进制协议头部
- **Message Type Flags修复**：完整客户端请求使用无序列号标志(0000)，音频请求使用正确标志
- **序列化方法修正**：音频数据使用无序列化(0000)，JSON数据使用JSON序列化(0001)
- **压缩标志优化**：正确设置GZIP压缩标志，音频数据和JSON数据分别处理
- **移除错误序列号**：完整客户端请求不需要序列号，简化协议实现
- **官方文档对照**：严格按照teache.md官方协议文档实现所有细节
- **错误响应处理**：优化服务器错误响应的解析和处理机制

### ASR语音识别协议完全修复 🎤🔧✅
- **音频格式崩溃修复**：修复`IsFormatSampleRateAndChannelCountValid(format)`崩溃错误
- **自定义音频格式**：创建符合ASR要求的16kHz单声道Float32格式，而不是直接使用设备格式
- **音频格式转换器**：添加AVAudioConverter实现输入格式到目标格式的实时转换
- **GZIP压缩支持**：实现完整的GZIP压缩和解压缩，与Python参考代码保持一致
- **协议头部修复**：修正压缩标志位，正确设置GZIP压缩标志(0x11)
- **序列号管理优化**：实现正确的序列号递增和负值标记机制
- **二进制协议完善**：完全按照火山引擎官方Python示例实现二进制协议
- **WebSocket连接优化**：添加连接状态监控和自动重连机制，提升连接稳定性
- **音频缓冲机制**：实现音频数据缓冲，500ms发送一次，减少服务器压力
- **错误响应处理**：完善服务器错误响应解析，支持GZIP解压缩
- **连接丢失恢复**：检测到连接丢失时自动重连，确保录音过程不中断
- **音频数据优化**：增大音频缓冲区到4096，提升音频质量和传输效率
- **最后数据发送**：停止录音时确保发送所有缓冲数据，避免数据丢失
- **识别结果解析**：优化ASR响应解析，正确提取文本识别结果
- **调试信息完善**：详细的连接状态、音频格式、数据传输日志

### 火山引擎ASR音频格式修复 🎤✅
- **音频格式问题解决**：修复"Invalid audio format"错误，服务器要求WAV格式而不是原始PCM
- **WAV格式封装**：实现完整的WAV文件头部生成，包括RIFF、fmt、data子块
- **音频参数配置**：正确设置16kHz采样率、16位深度、单声道参数
- **二进制协议解析成功**：协议解析器正常工作，能正确提取服务器响应
- **错误信息识别**：成功识别并显示服务器返回的具体错误信息
- **连接认证通过**：WebSocket连接和HTTP头部认证完全正常
- **数据传输优化**：音频数据正确转换为小端字节序WAV格式
- **调试信息完善**：详细的协议头部解析和payload内容显示

### 火山引擎ASR大模型流式语音识别集成 🎤🔥
- **API端点更新**：使用正确的大模型ASR端点`/api/v3/sauc/bigmodel`
- **HTTP头部认证**：实现标准的X-Api-*头部认证方式，包括App-Key、Access-Key、Resource-Id、Connect-Id
- **二进制协议实现**：根据官方Python示例实现完整的二进制通信协议
- **资源ID配置**：使用小时版大模型流式语音识别`volc.bigasr.sauc.duration`
- **完整客户端请求**：实现包含用户信息、音频配置、请求参数的完整初始化请求
- **音频数据协议**：使用二进制协议发送压缩的PCM音频数据
- **序列号管理**：正确实现消息序列号和最后包标记机制
- **数据压缩**：使用zlib压缩JSON配置和音频数据，提高传输效率
- **协议头部构建**：实现标准的协议版本、消息类型、序列化和压缩标志
- **连接ID追踪**：使用UUID生成连接ID，便于服务端日志追踪和问题排查

### 麦克风权限配置修复 🎤✅
- **权限配置完成**：在Xcode项目设置中添加了`NSMicrophoneUsageDescription`权限描述
- **配置方式统一**：采用与定位权限相同的`INFOPLIST_KEY_`前缀配置方式
- **Debug和Release双配置**：确保在所有构建配置中都包含麦克风权限描述
- **权限描述优化**：提供清晰的权限使用说明，告知用户麦克风用于语音识别功能
- **崩溃问题解决**：修复应用访问麦克风时因缺少权限描述而崩溃的问题
- **配置文件更新**：同步更新项目配置状态，标记权限配置为已完成

### ASR语音识别功能集成 🎤
- **火山引擎ASR集成**：集成火山引擎语音识别服务，支持实时语音转文字
- **iOS原生语音识别备选**：使用iOS原生Speech框架作为备选方案，确保功能可用性
- **智能识别模式切换**：默认使用火山引擎ASR，可切换到iOS原生识别
- **实时语音反馈**：录音时显示动态识别状态和实时文字转换
- **权限管理优化**：自动申请麦克风和语音识别权限，提供友好的权限说明
- **语音转文字自动发送**：识别完成后自动将文字发送给AI朋友进行对话
- **错误处理完善**：完整的错误处理机制，识别失败时提供清晰的错误提示
- **音频会话优化**：优化音频会话设置，支持蓝牙耳机和外放模式
- **WebSocket连接**：实现与火山引擎ASR服务的WebSocket实时连接
- **签名认证**：实现火山引擎API的签名认证机制
- **多语言支持**：主要支持中文语音识别，可扩展其他语言
- **录音状态可视化**：录音按钮动态效果，清晰显示录音和识别状态
- **项目配置优化**：解决Info.plist冲突问题，提供详细的Xcode配置指南
- **编译错误修复**：修复Main Actor隔离和权限申请的编译问题

### 聊天界面重新设计 🎨
- **页面名称更新**：将"AI朋友"页面更名为"聊天"，Tab图标改为消息图标
- **输入栏重新设计**：采用类似微信的输入栏设计，提升用户体验
- **语音/文本切换**：左侧添加语音/文本模式切换按钮，支持按住录音功能
- **录音界面**：语音模式下显示"按住说话"按钮，支持长按录音交互
- **表情按钮优化**：在文本和语音模式下都保留表情按钮，方便快速发送表情
- **表情栏位置调整**：表情选择器现在显示在输入栏下方，而不是上方
- **更多选项面板**：发送按钮改为圆形加号，点击后在输入栏下方弹出选项面板
- **简化选项面板**：移除重复的表情按钮，只保留图像和视频通话两个选项
- **智能按钮切换**：有文本时显示发送按钮，无文本时显示更多选项按钮
- **视觉效果优化**：录音时按钮会放大并变红，提供清晰的视觉反馈
- **交互体验提升**：更符合现代聊天应用的交互习惯和用户期望

### AR导航功能完全移除 🔧
- **移除AR导航页面**：完全删除ARNavigationView.swift文件和所有AR相关功能
- **移除AR导航按钮**：地图页面不再显示AR导航切换按钮
- **移除AI弹窗提醒**：取消地图页面打开时的AI问候和AR导航提醒功能
- **简化地图体验**：地图页面现在专注于纯粹的传统导航功能，不再有AI干扰
- **代码清理**：移除所有AR相关的状态变量、函数和导入语句
- **AIService优化**：移除sendMapReminder和analyzeARSnapshot方法，精简AI服务接口
- **技术栈简化**：移除ARKit和AVFoundation依赖，专注于地图导航核心功能

### AI角色设定系统完善 🤖
- **完整的角色设定页面**：重新设计AI配置页面，提供直观的角色设定编辑界面
- **默认提示词显示**：页面默认显示当前代码中的系统提示词，用户可以查看和修改
- **数据库持久化**：AI角色设定保存在独立的SwiftData数据库中，重启应用后保持设定
- **实时预览编辑**：支持编辑模式和预览模式切换，编辑体验更加友好
- **恢复默认功能**：提供一键恢复默认设定按钮，方便用户重置角色设定
- **未保存提醒**：离开页面时如有未保存更改会弹出确认对话框
- **全局生效**：角色设定影响所有AI对话场景，包括聊天、图像识别等

### 左滑删除功能优化 🔧
- **极低触发门槛**：左滑距离降低到8点，几乎轻触即可触发删除按钮
- **直接跟随手势**：移除阻尼效果，卡片直接跟随手指移动，响应更加灵敏
- **最小滑动距离**：设置为1点，确保任何微小的滑动都能被识别
- **智能判断逻辑**：滑动超过按钮宽度20%即可锁定显示删除按钮

### 左滑删除功能优化 🔧
- **重构滑动机制**：完全重写左滑删除功能，使用更可靠的手势识别和动画系统
- **改进交互体验**：左滑超过40点即可触发删除按钮显示，响应更加灵敏
- **优化视觉反馈**：删除按钮使用流畅的过渡动画，视觉效果更加自然
- **手势冲突解决**：修复手势与按钮点击的冲突问题，确保操作准确性
- **调试信息添加**：临时添加控制台输出，便于调试和验证功能正常工作



### 地点管理和地图显示优化 🆕
- **地图地点显示修复**：修复地图页面"全部地点"计划不显示所有地点的问题，现在正确显示所有计划中的地点
- **地点卡片网格布局**：地点页面改为一行显示两个地点的网格布局，提升空间利用率和浏览体验
- **左滑删除功能**：新增地点卡片左滑露出删除按钮功能，提供更直观的删除操作体验
- **默认计划系统**：新增"默认计划"，位于"全部地点"之后，在"全部地点"中添加地点时自动添加到默认计划
- **系统计划保护**：防止删除"全部地点"和"默认计划"等系统计划，确保应用稳定性
- **计划视觉区分**：为系统计划添加特殊图标和颜色，"全部地点"显示地球图标，"默认计划"显示星星图标
- **卡片尺寸优化**：针对网格布局优化地点卡片尺寸和信息显示，确保在较小空间内信息清晰可读
- **交互体验提升**：删除操作增加动画效果，滑动手势响应更加流畅自然

### 地点管理功能全面优化 🔧
- **搜索体验优化**：修复搜索键盘回车后不再显示结果的问题，增加搜索延迟防抖
- **数据持久化修复**：为地点数据创建独立数据库文件，解决重启应用后地点丢失的问题
- **计划同步修复**：修复新建旅行计划在地图选择器中不显示的问题，确保数据一致性
- **独立数据库架构**：Places.store独立存储，避免与ChatHistory数据库冲突
- **数据操作优化**：创建和删除计划后自动重新加载数据，确保UI实时更新
- **"全部地点"功能增强**：现在"全部地点"会显示所有计划中的所有地点，作为聚合视图
- **智能地点分配**：在"全部地点"视图下添加地点时，自动分配到具体计划中
- **实时数据同步**：修复UI更新延迟问题，确保新建计划立即在所有页面显示

### 对话历史功能修复 🔧
- **历史消息加载修复**：修复AI聊天页面点击"查看更多历史消息"后消息消失的问题
- **消息ID一致性**：优化ChatMessage结构，确保历史消息和当前消息的ID保持一致
- **分页加载优化**：改进历史消息分页加载逻辑，正确合并历史消息和当前会话消息
- **个人中心导航修复**：修复个人中心对话历史页面点不开的问题，移除重复的设置项
- **数据模型改进**：增强ChatMessage初始化方法，支持从历史记录创建消息时保持原有ID
- **历史记录转换优化**：改进ChatHistory到ChatMessage的转换逻辑，保持时间戳和ID一致性

### 地点管理功能完整实现 🆕
- **完整地点管理系统**：新增地点Tab，实现地点搜索、添加、管理的完整流程
- **旅行计划功能**：支持创建多个旅行计划，每个计划独立管理地点列表
- **智能搜索添加**：集成MapKit地点搜索，实时显示搜索结果，一键添加到当前计划
- **卡片式地点展示**：美观的地点卡片设计，包含封面图、地址信息和操作按钮
- **地图集成显示**：地图页面根据选中计划显示对应地点标注，支持计划切换
- **计划选择器优化**：美观的计划选择界面，实时显示当前选中计划和地点数量
- **地点详情页面**：基础版本的地点详情展示，为后续功能扩展预留空间
- **数据持久化**：使用SwiftData完整实现地点和计划的本地存储
- **四Tab导航**：更新主导航为地图、地点、AI朋友、我的四个Tab结构

## 历史更新 (2025/7/30)

### 对话历史记录系统 🆕
- **完整历史记录**：使用SwiftData本地持久化存储所有对话记录
- **智能分页加载**：首次加载20条最近记录，上翻时加载10条更多历史
- **RAG记忆增强**：发送消息时自动挂载对话历史，让AI具有记忆能力
- **图像记录优化**：图像消息记录为简短描述格式 `[图像]:一个什么图像`
- **双版本图像识别**：生成详细版本用于聊天回复，简短版本用于历史记录
- **历史查看页面**：在个人中心可查看完整对话历史和统计信息
- **会话管理**：支持多会话管理和会话统计功能

### AI图像识别功能增强 🔥
- **双模型图像识别架构**：集成专用图像识别模型 `ep-20250730213933-4s55h`
- **智能识别流程**：先使用专用模型识别图像内容，再由角色扮演模型生成友好回复
- **高精度图像分析**：提升图像识别准确性和响应质量
- **智能回退机制**：识别失败时自动回退到原有多模态模型
- **旅行场景优化**：针对旅行相关图片提供专业建议和经验分享
- **AR导航集成**：AR截图分析也使用新的双模型架构
- **自然对话风格**：AI回复更像真正的朋友，简短自然，减少AI味道

### 地图导航功能增强 ✨
- **多种交通方式选择**：新增开车、步行、公交、骑车、打车五种交通方式
- **智能路线比较**：自动计算多种交通方式的路线，显示距离和时间
- **第三方应用集成**：支持跳转到滴滴出行、高德地图、百度地图、腾讯地图、Apple地图
- **应用可用性检测**：智能检测已安装的应用，未安装时引导到App Store
- **交通方式选择器**：美观的横向滚动选择器，实时显示路线信息
- **打车选项弹窗**：专门的打车应用选择界面，支持一键跳转
- **URL Scheme深度链接**：与第三方应用无缝集成，传递目的地信息
- **完整的单元测试**：覆盖交通方式、路线计算、打车选项等核心功能

### 问题修复 🔧
- **编译错误修复**：解决LocationManager重复定义导致的编译错误
- **共享位置管理器**：创建SharedLocationManager统一管理位置服务，避免代码重复
- **AR导航箭头修复**：添加实时方向指示器，箭头根据用户位置和朝向实时更新方向
- **对话历史页面修复**：修复个人中心对话历史页面打不开的问题
- **分页加载体验优化**：修复加载更多历史消息后自动滑到底部的问题，现在保持在原位置
- **位置服务集成**：AR导航集成CoreLocation，提供精确的方向和距离信息
- **公交路线优化**：改进公交路线计算，在不可用时显示友好提示
- **骑车路线实现**：基于步行路线计算骑车路线，提供合理的时间估算
- **第三方应用检测**：修复URL Scheme检测逻辑，正确识别已安装应用
- **高德地图集成**：修正高德地图URL参数，确保正确跳转到导航页面
- **应用权限配置**：添加LSApplicationQueriesSchemes权限，支持第三方应用检测
- **路线显示优化**：改进不同交通方式的时间和距离显示逻辑

### 技术改进
- **实时位置服务**：集成CoreLocation和方向传感器，提供精确的AR导航体验
- **智能方向计算**：基于地理坐标和用户朝向计算精确的方向指示
- **用户体验优化**：改进分页加载的滚动定位，保持用户浏览位置
- **SwiftData集成**：完整的本地数据持久化方案，支持对话历史存储
- **RAG记忆系统**：智能上下文管理，让AI具有长期记忆能力
- **分页加载优化**：高效的历史记录分页加载机制
- **双模型AI架构**：实现图像识别模型 + 角色扮演模型的协同工作
- **智能模型选择**：根据任务类型自动选择最适合的AI模型
- **错误处理优化**：完善图像识别失败时的回退机制
- **AI角色优化**：调整系统提示词，让AI回复更像朋友间的日常对话
- **对话风格改进**：简化问候语和回复模式，减少正式感和AI味道
- **数据模型优化**：设计高效的ChatHistory数据模型，支持多种消息类型
- 优化了交通方式枚举和MKDirectionsTransportType映射
- 新增RouteOption数据模型，支持多路线管理
- 完善了第三方应用URL Scheme处理
- 增强了用户体验和无障碍支持
- 添加了完整的功能测试覆盖
- 改进了路线计算的错误处理和用户反馈
